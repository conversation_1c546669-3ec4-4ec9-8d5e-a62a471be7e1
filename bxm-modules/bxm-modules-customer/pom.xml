<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.bxm</groupId>
    <artifactId>bxm-modules</artifactId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>bxm-modules-customer</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <description>
    bxm-modules-customer客户模块
  </description>

  <dependencies>

    <!-- SpringCloud Alibaba Nacos -->
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba.nacos</groupId>
          <artifactId>nacos-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.alibaba.nacos</groupId>
      <artifactId>nacos-client</artifactId>
      <version>${nacos.client.version}</version>
    </dependency>

    <!-- SpringCloud Alibaba Nacos Config -->
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>

    <!-- SpringCloud Alibaba Sentinel -->
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
    </dependency>

    <!-- SpringBoot Actuator -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
    </dependency>

    <!-- Swagger UI -->
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger-ui</artifactId>
      <version>${swagger.fox.version}</version>
    </dependency>

    <!-- Canal Client -->
    <dependency>
      <groupId>com.alibaba.otter</groupId>
      <artifactId>canal.client</artifactId>
      <version>${canal.client.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
      <version>4.9.4</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>
    <!-- Mysql Connector -->
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>

    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>${aliyun.oss.version}</version>
    </dependency>

    <!-- RuoYi Common DataSource -->
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-common-datasource</artifactId>
      <version>${bxm.common.datasource.version}</version>
    </dependency>

    <!-- RuoYi Common DataScope -->
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-common-datascope</artifactId>
      <version>${bxm.common.datascope.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-common-customize</artifactId>
      <version>${bxm.common.customize.version}</version>
    </dependency>

    <!-- RuoYi Common Log -->
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-common-log</artifactId>
      <version>${bxm.common.log.version}</version>
    </dependency>

    <!-- RuoYi Common Swagger -->
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-common-swagger</artifactId>
      <version>${bxm.common.swagger.version}</version>
    </dependency>

    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-common-mybatisplus</artifactId>
      <version>${bxm.common.mybatisplus.version}</version>
    </dependency>

    <dependency>
      <groupId>com.xuxueli</groupId>
      <artifactId>xxl-job-core</artifactId>
      <version>${xxl-job.version}</version>
    </dependency>

    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-api-thirdpart</artifactId>
      <version>${bxm.api.thirdpart.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-api-file</artifactId>
      <version>${bxm.api.file.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-api-system</artifactId>
      <version>${bxm.api.system.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-api-customer</artifactId>
      <version>${bxm.api.customer.version}</version>
    </dependency>
    <dependency>
      <groupId>com.bxm</groupId>
      <artifactId>bxm-common-rocketmq</artifactId>
      <version>${bxm.common.rocketmq.version}</version>
    </dependency>
      <dependency>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-all</artifactId>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>com.github.junrar</groupId>
          <artifactId>junrar</artifactId>
          <scope>compile</scope>
      </dependency>


  </dependencies>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
