### 增值交付单Controller HTTP测试文件
### 测试ValuedAddedDeliveryOrderController核心业务流程
### 基于DeliveryOrderUpsertReq参数的主流程测试场景
###
### 字段说明：
### - valueAddedItemTypeId: 增值事项类型 (1-6)
### - taxpayerType: 纳税性质 (1-小规模纳税人, 2-一般纳税人)
### - accountingInfo: 账务类型信息 (STANDARD-标准账务, NON_STANDARD-非标账务)
### - nationalTaxAccount: 国税账号对象
### - personalTaxAccount: 个税账号对象
###
### 核心测试用例覆盖：
### 1-20: 核心业务流程测试（新增、更新、验证、业务场景）

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImI1MDY1Y2U4LTQ1YWEtNDY4NS04ZDA4LTFiYWRiZGNjYTNmOSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.dpOEuZ23jlvjggqyFx25GdhbzUP_NYVRzQpV-qObGxymYRlBhnGha58gX7UIOKen4ygdHIqJc8hEAmE96KiMVw

### ========================================
### 完整性测试 - upsert方法综合测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo":"*******************",
  "customerName": "完整性测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 4,
  "itemName": "完整性测试",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": true,
  "syncReassignment": true,
  "syncContactPerson": true,
  "syncAccountChange": true,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED"]
  },
  "requirements": "完整性测试的交付要求，包含所有必要的材料和流程说明",
  "taxRequirement": "增值税税负率控制在3%以内",
  "ddl": "2025-12-31",
  "customerId": 1001,
  "nationalTaxAccount": {
    "accountNumber": "91350105MA2Y9DXW8L",
    "password": "NationalTax123!",
    "loginMethod": "账号密码登录",
    "realNameAgent": "张三",
    "mobile": "***********",
    "idNumber": "350105199001011234",
    "remark": "国税账号测试备注",
    "operationType": 1
  },
  "personalTaxAccount": {
    "accountNumber": "91350105MA2Y9DXW8L",
    "password": "PersonalTax456!",
    "loginMethod": "手机验证码登录",
    "realNameAgent": "李四",
    "mobile": "***********",
    "idNumber": "350105199002022345",
    "remark": "个税账号测试备注",
    "operationType": 1
  }
}



### ========================================
### saveStatus 方法测试 - 正常场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430003E3F",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "totalWithholdingAmount": 1500.00,
  "remark": "待交付状态保存测试"
}

### ========================================
### saveStatus 方法测试 - 验证失败场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "remark": "交付单编号为空测试"
}

### ========================================
### 查询接口测试
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?customerName=测试企业&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 生成交付单编号接口测试
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/genDeliveryOrderNo
Authorization: {{authorization}}

### ========================================
### 根据编号查询接口测试
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getByOrderNo/VAD2508051430003E3F
Authorization: {{authorization}}
