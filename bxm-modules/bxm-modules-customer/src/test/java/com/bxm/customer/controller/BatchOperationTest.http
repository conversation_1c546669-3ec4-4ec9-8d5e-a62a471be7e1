### 批量操作增值交付单 - 批量确认
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_CONFIRM",
  "reason": "批量确认交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量确认操作"
}

### 批量操作增值交付单 - 批量提交
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_SUBMIT",
  "reason": "批量提交交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量提交操作"
}

### 批量操作增值交付单 - 批量关闭扣款
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_CLOSE_DEDUCTION",
  "reason": "批量关闭扣款",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量关闭扣款操作"
}

### 批量操作增值交付单 - 批量关闭交付
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_CLOSE_DELIVERY",
  "reason": "批量关闭交付",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量关闭交付操作"
}

### 批量操作增值交付单 - 批量解除扣款异常
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RESOLVE_DEDUCTION_EXCEPTION",
  "reason": "批量解除扣款异常",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量解除扣款异常操作"
}

### 批量操作增值交付单 - 批量解除交付异常
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RESOLVE_DELIVERY_EXCEPTION",
  "reason": "批量解除交付异常",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量解除交付异常操作"
}

### 批量操作增值交付单 - 批量驳回
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_REJECT",
  "reason": "批量驳回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量驳回操作"
}

### 批量操作增值交付单 - 批量退回（已扣款 → 待扣款）
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RETURN",
  "sourceStatus": "DEDUCTION_COMPLETED",
  "reason": "批量退回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量退回操作：已扣款 → 待扣款"
}

### 批量操作增值交付单 - 批量退回（待扣款 → 待确认）
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RETURN",
  "sourceStatus": "CONFIRMED_PENDING_DEDUCTION",
  "reason": "批量退回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量退回操作：待扣款 → 待确认"
}

### 批量操作增值交付单 - 批量退回（待确认 → 待交付）
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RETURN",
  "sourceStatus": "PENDING_CONFIRMATION",
  "reason": "批量退回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量退回操作：待确认 → 待交付"
}

### 批量操作增值交付单 - 批量退回（待交付 → 待提交）
POST {{host}}/customer/valueAdded/deliveryOrder/batchOperation
Content-Type: application/json

{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_RETURN",
  "sourceStatus": "SUBMITTED_PENDING_DELIVERY",
  "reason": "批量退回交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量退回操作：待交付 → 待提交"
}

### 导出批量操作异常数据
GET {{host}}/customer/valueAdded/deliveryOrder/exportBatchErrors/{{batchNo}}

### 示例：导出批量操作异常数据（使用具体的批次号）
GET {{host}}/customer/valueAdded/deliveryOrder/exportBatchErrors/a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6

### ========================================
### 分派增值交付单测试
### ========================================

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/x-www-form-urlencoded
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImQ2NTJlYjU4LTY5MGMtNGM1My1iYzI3LTUxOWU2YWVlNzFhMCIsInVzZXJuYW1lIjoiYWRtaW4ifQ.4N-7XBmzEAlAFskXuNcR5kcDYzuodV0z3SMC-WZtwPTtuHwjGF9nerjXTCjvHJCyt2lI1t0SFVD2ZbCpswEIcQ


### 1. 正常分派测试 - 分派2个交付单到会计部门
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=VAD2508051430001A1C&deliveryOrders=VAD2508051430002A1C&accountingDeptId=1001


### 3. 批量分派测试 - 分派5个交付单
POST {{baseUrl}}/valuedAddedDeliveryOrder/dispatch
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrders=*******************&deliveryOrders=*******************&accountingDeptId=1003
