package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 已确认待扣款到扣款完成状态变更策略
 *
 * 处理从"已确认待扣款"状态到"扣款完成"状态的转换变更
 *
 * 支持的状态转换：
 * CONFIRMED_PENDING_DEDUCTION -> DEDUCTION_COMPLETED (扣款正常完成)
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class ConfirmedPendingDeductionStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedSourceStatuses() {
        return Arrays.asList(ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION, ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION);
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        // 只处理 DEDUCTION_COMPLETED 目标状态
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            validateCustomerInfo(order);
            validateCreditCode(order);
            validateTaxpayerType(order);
            if (order.getSyncHandlingFee() == null) {
                throw new IllegalArgumentException("是否同步手续费标志不能为空");
            }
        } else {
            throwUnsupportedTransition("已确认待扣款", request.getTargetStatus());
        }
    }


}
