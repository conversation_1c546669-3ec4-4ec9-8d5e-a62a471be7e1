package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.ValueAddedAccountPeriodService;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 已提交待交付到待确认状态变更策略
 *
 * 处理从"已提交待交付"状态到"已交付待确认"状态的转换变更
 *
 * 支持的状态转换：
 * SUBMITTED_PENDING_DELIVERY -> PENDING_CONFIRMATION (正常交付完成)
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class SubmittedPendingDeliveryStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Autowired
    private ValueAddedAccountPeriodService valueAddedAccountPeriodService;

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedSourceStatuses() {
        return Arrays.asList(ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY);
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        // 只处理 PENDING_CONFIRMATION 目标状态
        if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            validateNotEmpty(order.getRequirements(), "交付要求");
            if (order.getBusinessDeptId() == null) {
                throw new IllegalArgumentException("业务部门ID不能为空");
            }
        } else {
            throwUnsupportedTransition("已提交待交付", request.getTargetStatus());
        }
    }


}
