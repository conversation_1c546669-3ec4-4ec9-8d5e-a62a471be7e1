package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 草稿保存为待提交状态变更策略
 *
 * 处理从"草稿"状态保存为"待提交"状态的转换
 * 
 * 优化后的策略模式：
 * - 每个策略只负责一个目标状态
 * - 根据目标状态直接查找对应策略
 * - 提高了代码的可维护性和扩展性
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class DraftStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.DRAFT;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedSourceStatuses() {
        return Arrays.asList(ValueAddedDeliveryOrderStatus.DRAFT);
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        // 保存为待提交状态的验证逻辑
        validateNotEmpty(order.getCustomerName(), "客户名称");
        validateCreditCode(order);
        if (order.getBusinessTopDeptId() == null) {
            throw new IllegalArgumentException("顶级业务部门不能为空");
        }
    }
}
