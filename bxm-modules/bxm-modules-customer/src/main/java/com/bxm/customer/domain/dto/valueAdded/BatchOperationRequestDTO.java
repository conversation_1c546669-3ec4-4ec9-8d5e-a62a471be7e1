package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.customer.domain.enums.ValueAddedBatchOperationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量操作请求DTO
 *
 * 用于增值交付单批量操作的请求参数封装
 * 支持多种批量操作类型，包括批量确认、提交、关闭、处理异常、驳回、退回等
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作请求DTO")
public class BatchOperationRequestDTO {

    /**
     * 交付单编号列表
     */
    @NotEmpty(message = "交付单编号列表不能为空")
    @Size(max = 1000, message = "单次批量操作最多支持1000条记录")
    @ApiModelProperty(value = "交付单编号列表", required = true, example = "[\"VAD2508051430001A1C\", \"VAD2508051430002A1C\"]")
    private List<String> deliveryOrderNos;

    /**
     * 操作类型（与StatusChangeRequestDTO的operTypeName保持一致）
     */
    @ApiModelProperty(value = "操作类型", required = true, example = "BATCH_CONFIRM",
                     allowableValues = "BATCH_CONFIRM,BATCH_SUBMIT,BATCH_CLOSE_DEDUCTION,BATCH_CLOSE_DELIVERY,BATCH_RESOLVE_DEDUCTION_EXCEPTION,BATCH_RESOLVE_DELIVERY_EXCEPTION,BATCH_REJECT,BATCH_RETURN")
    private ValueAddedBatchOperationType operationType;

    /**
     * 操作类型名称（与StatusChangeRequestDTO保持一致的字段名）
     */
    @ApiModelProperty(value = "操作类型名称", example = "check", notes = "前端传递操作类型代码，与StatusChangeRequestDTO的operTypeName字段保持一致")
    private String operTypeName;

    /**
     * 目标状态（与StatusChangeRequestDTO保持一致，必填）
     */
    @NotBlank(message = "目标状态不能为空")
    @Size(max = 50, message = "目标状态长度不能超过50个字符")
    @ApiModelProperty(value = "目标状态", required = true, example = "CONFIRMED_PENDING_DEDUCTION")
    private String targetStatus;

    /**
     * 前置状态（批量退回操作时必填，用于确定目标状态）
     */
    @ApiModelProperty(value = "前置状态，批量退回操作时必填", example = "DEDUCTION_COMPLETED")
    private String sourceStatus;

    /**
     * 操作原因
     */
    @Size(max = 500, message = "操作原因长度不能超过500个字符")
    @ApiModelProperty(value = "操作原因", example = "批量确认交付单")
    private String reason;



    /**
     * 备注信息
     */
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    @ApiModelProperty(value = "备注信息", example = "批量操作备注")
    private String remark;

    /** 顶级业务部门id */
    @ApiModelProperty(value = "集团id")
    private Long businessTopDeptId;

    private Long deptId;

    /**
     * 验证批量退回操作的前置状态
     */
    public void validateBatchReturnSourceStatus() {
        if (operationType == ValueAddedBatchOperationType.BATCH_RETURN) {
            if (sourceStatus == null || sourceStatus.trim().isEmpty()) {
                throw new IllegalArgumentException("批量退回操作必须指定前置状态");
            }
        }
    }

    /**
     * 获取操作描述
     */
    public String getOperationDescription() {
        if (operationType == null) {
            return "未知操作";
        }
        return operationType.getDescription();
    }

    /**
     * 获取交付单数量
     */
    public int getOrderCount() {
        return deliveryOrderNos == null ? 0 : deliveryOrderNos.size();
    }
}
