package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 扣款异常关闭状态变更策略
 *
 * 处理从"扣款异常(待确认)"状态到"扣款关闭"状态的转换变更
 *
 * 支持的状态转换：
 * DEDUCTION_EXCEPTION -> DEDUCTION_CLOSED (关闭扣款)
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class DeductionExceptionStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedSourceStatuses() {
        return Arrays.asList(ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION);
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        // 只处理 DEDUCTION_CLOSED 目标状态
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED) {
            validateCloseOperation(request, "扣款");
        } else {
            throwUnsupportedTransition("扣款异常", request.getTargetStatus());
        }
    }


}
