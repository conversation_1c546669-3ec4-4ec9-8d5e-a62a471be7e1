package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.TextVO;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.DictUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.BatchDeleteRequestDTO;
import com.bxm.customer.domain.dto.DispatchRequestDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.valueAdded.DispatchResultDTO;
import com.bxm.customer.domain.dto.valueAdded.DispatchErrorDTO;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.*;
import com.bxm.customer.domain.enums.AccountMainType;
import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderUpsertReq;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderVO;
import com.bxm.customer.domain.vo.valueAdded.SaveStatusReqVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedItemTypeVO;
import com.bxm.customer.service.*;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.customer.config.BatchOperationConfig;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.system.api.domain.SysDictData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 增值交付单Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/valuedAddedDeliveryOrder")
@Api(tags = "增值交付单管理")
public class ValuedAddedDeliveryOrderController extends BaseController {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @Autowired
    private IBatchValueAddedOperationService batchOperationService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BatchOperationConfig batchOperationConfig;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;


    /**
     * 新增或更新增值交付单
     *
     * @param orderVO 增值交付单VO
     * @return 操作结果
     */
    @PostMapping("/upsert")
    @ApiOperation(value = "新增或更新增值交付单", notes = "支持新增和更新操作，根据ID或交付单编号自动判断")
    @Log(title = "Upsert value added delivery order", businessType = BusinessType.INSERT)
    public Result<DeliveryOrderUpsertReq> upsert(@Valid @RequestBody DeliveryOrderUpsertReq orderVO,
                                                 @RequestHeader("deptId") Long deptId) {
        try {
            orderVO.setDeptId(deptId);
            log.info("Upsert delivery order request: {}", orderVO.getCustomerName());
            // 调用服务层进行upsert操作
            ValueAddedDeliveryOrder result = valueAddedDeliveryOrderService.upsert(orderVO);
            // 转换为VO返回
            DeliveryOrderUpsertReq resultVO = new DeliveryOrderUpsertReq();
            BeanUtils.copyProperties(result, resultVO);
            log.info("Upsert delivery order success: {}", result.getDeliveryOrderNo());
            return Result.ok(resultVO);
        } catch (IllegalArgumentException e) {
            log.warn("Upsert delivery order validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Upsert delivery order failed for customer: {}", orderVO.getCustomerName(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 提交到待交付状态
     * 先调用upsert接口，然后校验并转换状态为SUBMITTED_PENDING_DELIVERY
     *
     * @param orderVO 增值交付单VO
     * @return 操作结果
     */
    @PostMapping("/submit")
    @ApiOperation(value = "提交到待交付状态", notes = "先执行upsert操作，然后将状态转换为已提交待交付")
    @Log(title = "Submit delivery order to pending delivery", businessType = BusinessType.UPDATE)
    public Result<DeliveryOrderUpsertReq> submit(@Valid @RequestBody DeliveryOrderUpsertReq orderVO,
                                                 @RequestHeader("deptId") Long deptId) {
        try {
            orderVO.setDeptId(deptId);
            log.info("Submit to pending delivery request: {}", orderVO.getCustomerName());
            // 调用服务层进行提交到待交付操作
            ValueAddedDeliveryOrder result = valueAddedDeliveryOrderService.submitToPendingDelivery(orderVO);
            // 转换为VO返回
            DeliveryOrderUpsertReq resultVO = new DeliveryOrderUpsertReq();
            BeanUtils.copyProperties(result, resultVO);
            log.info("Submit to pending delivery success: {}", result.getDeliveryOrderNo());
            return Result.ok(resultVO);
        } catch (IllegalArgumentException e) {
            log.warn("Submit to pending delivery validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Submit to pending delivery failed for customer: {}", orderVO.getCustomerName(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 条件查询增值交付单
     * 所有条件在 service 层进行动态拼接
     */
    @GetMapping("/query")
    @ApiOperation(value = "增值交付单查询", notes = "按条件分页查询增值交付单；条件均为可选")
    @Log(title = "Query value added delivery order", businessType = BusinessType.OTHER)
    public Result<IPage<DeliveryOrderVO>> query(DeliveryOrderQuery query, @RequestHeader("deptId") Long deptId) {
        try {
            // 校验发起时间参数
            Date initiateTimeStart = query.getInitiateTimeStart();
            Date initiateTimeEnd = query.getInitiateTimeEnd();

            // 校验：如果传了一个时间参数，必须同时传两个
            if ((initiateTimeStart != null && initiateTimeEnd == null) ||
                (initiateTimeStart == null && initiateTimeEnd != null)) {
                return Result.fail("发起时间查询参数必须同时传递开始时间和结束时间");
            }

            // 校验：开始时间必须小于等于结束时间
            if (initiateTimeStart != null && initiateTimeEnd != null) {
                if (initiateTimeStart.after(initiateTimeEnd)) {
                    return Result.fail("发起时间开始时间必须小于等于结束时间");
                }
            }
            query.setDeptId(deptId);
            // 创建分页对象
            IPage<DeliveryOrderVO> page = new Page<>(
                query.getPageNum() != null ? query.getPageNum() : 1,
                query.getPageSize() != null ? query.getPageSize() : 10
            );
            // 调用Service层进行分页查询
            IPage<DeliveryOrderVO> result = valueAddedDeliveryOrderService.queryVOPage(page, query);

            log.info("Query delivery orders success, total: {}, current page: {}",
                result.getTotal(), result.getCurrent());
            return Result.ok(result);
        } catch (Exception e) {
            log.error("Query delivery orders failed", e);
            return Result.fail("查询增值交付单失败: " + e.getMessage());
        }
    }

    /**
     * 根据交付单编号查询增值交付单详细信息
     *
     * @param deliveryOrderNo 交付单编号
     * @return 查询结果，包含基本信息和关联的扩展数据，其中国税账号和个税账号包含转换为TextVO格式的账务信息
     */
    @GetMapping("/getDeliveryOrder/{deliveryOrderNo}")
    @ApiOperation(value = "根据交付单编号查询详细信息", notes = "根据交付单编号查询增值交付单详情，包含国税账号、个税账号（含TextVO格式账务信息）、员工信息列表、交付文件等扩展数据")
    @Log(title = "Get delivery order details by order number", businessType = BusinessType.OTHER)
    public Result<DeliveryOrderVO> getDeliveryOrder(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Query delivery order details by order number: {}", deliveryOrderNo);
            DeliveryOrderVO orderVO = valueAddedDeliveryOrderService.getDeliveryOrderVO(deliveryOrderNo);
            if (orderVO == null) {
                return Result.fail("未找到对应的增值交付单");
            }
            log.info("Query delivery order details success: {}", deliveryOrderNo);
            return Result.ok(orderVO);
        } catch (Exception e) {
            log.error("Failed to query delivery order details by order number: {}", deliveryOrderNo, e);
            return Result.fail("查询增值交付单详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 生成交付单编号
     * 编号规则：VAD + yyMMddHHmmsss + 3位随机码，总长度19位
     */
    @GetMapping("/genDeliveryOrderNo")
    @ApiOperation(value = "生成交付单编号", notes = "生成唯一的增值交付单编号，格式：VAD+时间戳到毫秒+随机码，总长度19位")
    @Log(title = "Generate delivery order number", businessType = BusinessType.OTHER)
    public Result<String> genDeliveryOrderNo() {
        try {
            // 生成时间戳部分：yyMMddHHmmssSSS格式（13位）
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"));
            // 生成3位随机码
            String randomCode = StringUtils.generateRandomCode(2);
            // 组合生成最终编号
            String deliveryOrderNo = "SW" + timestamp + randomCode;
            log.info("Generated delivery order number: {}", deliveryOrderNo);
            return Result.ok(deliveryOrderNo);
        } catch (Exception e) {
            log.error("Failed to generate delivery order number", e);
            return Result.fail("生成交付单编号失败");
        }
    }

    /**
     * 查询增值事项类型列表
     *
     * @return 增值事项类型列表
     */
    @GetMapping("/listItemType")
    @ApiOperation(value = "查询增值事项类型列表", notes = "获取所有可用的增值事项类型")
    @Log(title = "List value added item types", businessType = BusinessType.OTHER)
    public Result<List<ValueAddedItemTypeVO>> listItemType() {
        try {
            log.info("Query value added item type list");
            List<ValueAddedItemTypeVO> itemTypes = valueAddedItemTypeService.listItemTypeVO();
            log.info("Query value added item type list success, count: {}", itemTypes.size());
            return Result.ok(itemTypes);
        } catch (Exception e) {
            log.error("Query value added item type list failed", e);
            return Result.fail("查询增值事项类型列表失败");
        }
    }

    /**
     * 修改增值交付单状态
     *
     * @param request 状态变更请求
     * @return 操作结果
     */
    @PostMapping("/changeStatus")
    @ApiOperation(value = "修改增值交付单状态", notes = "通过状态机管理器修改交付单状态，包含完整的业务验证")
    @Log(title = "Change delivery order status", businessType = BusinessType.UPDATE)
    public Result<String> changeStatus(@Valid @RequestBody StatusChangeRequestDTO request,
                                       @RequestHeader("deptId") Long deptId) {
        try {
            request.setDeptId(deptId);
            log.info("Change status request for order: {} to status: {}", request.getDeliveryOrderNo(), request.getTargetStatus());
            // 调用服务层进行状态变更
            valueAddedDeliveryOrderService.changeStatus(request);
            log.info("Change status success for order: {} to status: {}", request.getDeliveryOrderNo(), request.getTargetStatus());
            return Result.ok("状态修改成功");
        } catch (IllegalArgumentException e) {
            log.warn("Change status validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Change status failed for order: {}", request != null ? request.getDeliveryOrderNo() : "unknown", e);
            return Result.fail("状态修改失败");
        }
    }

    /**
     * 获取指定交付单的可用状态列表
     *
     * @param deliveryOrderNo 交付单编号
     * @return 可用状态列表
     */
    @GetMapping("/availableStatuses/{deliveryOrderNo}")
    @ApiOperation(value = "获取可用状态列表", notes = "根据当前状态获取所有可以转换到的目标状态")
    @Log(title = "Get available statuses", businessType = BusinessType.OTHER)
    public Result<List<ValueAddedDeliveryOrderStatus>> getAvailableStatuses(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Get available statuses for order: {}", deliveryOrderNo);

            List<ValueAddedDeliveryOrderStatus> availableStatuses =
                    valueAddedDeliveryOrderService.getAvailableStatuses(deliveryOrderNo);

            log.info("Get available statuses success for order: {}, count: {}", deliveryOrderNo, availableStatuses.size());
            return Result.ok(availableStatuses);

        } catch (IllegalArgumentException e) {
            log.warn("Get available statuses validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Get available statuses failed for order: {}", deliveryOrderNo, e);
            return Result.fail("获取可用状态列表失败");
        }
    }

    /**
     * 保存状态信息
     *
     * @param request 保存状态请求
     * @return 操作结果
     */
    @PostMapping("/saveStatus")
    @ApiOperation(value = "保存状态信息", notes = "用于待交付、待扣款状态的保存操作")
    @Log(title = "Save delivery order status", businessType = BusinessType.UPDATE)
    public Result<String> saveStatus(@Valid @RequestBody SaveStatusReqVO request) {
        try {
            log.info("Save status request for order: {} to status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());

            // 调用服务层进行状态保存
            valueAddedDeliveryOrderService.saveStatus(request);

            log.info("Save status success for order: {} to status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());
            return Result.ok("状态保存成功");
        } catch (IllegalArgumentException e) {
            log.warn("Save status validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Save status failed for order: {}",
                    request != null ? request.getDeliveryOrderNo() : "unknown", e);
            return Result.fail("状态保存失败");
        }
    }



    /**
     * 批量操作增值交付单
     *
     * @param request 批量操作请求
     * @return 操作结果
     */
    @PostMapping("/batchOperation")
    @ApiOperation(value = "批量操作增值交付单", notes = "支持批量确认、提交、关闭、处理异常、驳回、退回等操作")
    @Log(title = "Batch operation delivery orders", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResultDTO> batchOperation(@Valid @RequestBody BatchOperationRequestDTO request,
                                                          @RequestHeader("deptId") Long deptId) {
        try {
            request.setDeptId(deptId);
            // 执行批量操作
            BatchOperationResultDTO result = batchOperationService.executeBatchOperation(request);

            log.info("Batch operation completed: {}, total: {}, success: {}, error: {}",
                    request.getOperationType().getDescription(),
                    result.getTotalCount(), result.getSuccessCount(), result.getErrorCount());

            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch operation validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch operation failed: {}", e.getMessage(), e);
            return Result.fail("批量操作失败");
        }
    }

    /**
     * 导出批量操作异常数据
     *
     * @param batchNo 批次号
     * @param response HTTP响应
     */
    @GetMapping("/exportBatchErrors/{batchNo}")
    @ApiOperation(value = "导出批量操作异常数据", notes = "根据批次号导出批量操作过程中产生的异常数据")
    @Log(title = "Export batch operation errors", businessType = BusinessType.EXPORT)
    public void exportBatchErrors(@PathVariable String batchNo, HttpServletResponse response) {
        try {
            log.info("Export batch operation errors for batchNo: {}", batchNo);

            // 获取异常数据
            List<BatchOperationErrorDTO> errorList = batchOperationService.getBatchOperationErrors(batchNo);

            if (errorList.isEmpty()) {
                throw new RuntimeException("未找到异常数据或数据已过期");
            }

            // 导出Excel
            ExcelUtil<BatchOperationErrorDTO> util = new ExcelUtil<>(BatchOperationErrorDTO.class);
            util.exportExcel(response, errorList, "批量操作异常数据");

            log.info("Export batch operation errors completed for batchNo: {}, count: {}", batchNo, errorList.size());
        } catch (Exception e) {
            log.error("Export batch operation errors failed for batchNo: {}", batchNo, e);
            throw new RuntimeException("导出异常数据失败: " + e.getMessage());
        }
    }

    /**
     * 分派增值交付单
     *
     * @param request 分派请求参数
     * @return 操作结果
     */
    @PostMapping("/dispatch")
    @ApiOperation(value = "分派增值交付单", notes = "批量将交付单分派给指定的会计部门")
    @Log(title = "Dispatch delivery orders", businessType = BusinessType.UPDATE)
    public Result<DispatchResultDTO> dispatch(@Valid @RequestBody DispatchRequestDTO request,
                                              @RequestHeader("deptId") Long deptId) {
        try {
            request.setDeptId(deptId);
            // 调用服务层进行分派操作
            DispatchResultDTO result = valueAddedDeliveryOrderService.batchDispatch(
                    request.getDeliveryOrders(),
                    request.getAccountingDeptId(),
                    request);

            log.info("Dispatch completed: total={}, success={}, failed={}, batchNo={}",
                    result.getTotal(), result.getSuccCnt(), result.getFailCnt(), result.getBatchNo());
            return Result.ok(result, result.getSummary());
        } catch (IllegalArgumentException e) {
            log.warn("Dispatch validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Dispatch failed for accounting dept: {}",
                    request != null ? request.getAccountingDeptId() : "unknown", e);
            return Result.fail("分派失败: " + e.getMessage());
        }
    }

    /**
     * 删除增值交付单
     *
     * @param deliveryOrderNo 交付单编号
     * @return 操作结果
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除增值交付单", notes = "删除指定的交付单")
    @Log(title = "Delete delivery order", businessType = BusinessType.DELETE)
    public Result<String> del(@RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo,
                              @RequestHeader("deptId") Long deptId) {
        try {
            log.info("Delete delivery order request: {}", deliveryOrderNo);
            // 基础参数验证
            if (StringUtils.isEmpty(deliveryOrderNo)) {
                return Result.fail("交付单编号不能为空");
            }
            // 获取交付单信息用于日志记录
            ValueAddedDeliveryOrder order = valueAddedDeliveryOrderService.getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                return Result.fail("交付单不存在");
            }
            // 执行删除操作
            boolean result = valueAddedDeliveryOrderService.removeById(order.getId());
            if (result) {
                // 记录删除操作日志
                saveDeleteBusinessLog(order.getId(), deliveryOrderNo, order.getCustomerName(), deptId);
                return Result.ok("删除成功");
            } else {
                return Result.fail("删除失败");
            }
        } catch (Exception e) {
            log.error("Delete delivery order failed: {}", deliveryOrderNo, e);
            return Result.fail("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除增值交付单
     *
     * @param request 批量删除请求参数
     * @return 操作结果
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除增值交付单", notes = "批量删除符合条件的交付单，删除条件：必须为已关闭交付、已扣款、已关闭扣款状态")
    @Log(title = "Batch delete delivery orders", businessType = BusinessType.DELETE)
    public Result<BatchOperationResultDTO> batchDelete(@Valid @RequestBody BatchDeleteRequestDTO request) {
        try {
            // 从SecurityUtils获取当前操作人信息
            Long operatorId = SecurityUtils.getUserId();
            String operatorName = SecurityUtils.getUsername();

            log.info("Batch delete request: order count: {}, operator: {} ({})",
                    request.getDeliveryOrderNos().size(), operatorName, operatorId);

            // 执行批量删除
            BatchOperationResultDTO result = batchOperationService.executeBatchDelete(
                    request.getDeliveryOrderNos(),request.getReason());

            log.info("Batch delete completed: total: {}, success: {}, error: {}",
                    result.getTotalCount(), result.getSuccessCount(), result.getErrorCount());

            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch delete validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch delete failed: {}", e.getMessage(), e);
            return Result.fail("批量删除失败");
        }
    }

    /**
     * 获取账户子类型
     *
     * @return 账户子类型列表，格式为TextVO数组，key为英文，value为中文
     */
    @GetMapping("/getAccountSubType")
    @ApiOperation(value = "获取账户子类型", notes = "根据账务主类型获取对应的账户子类型列表，返回key-value格式的TextVO数组")
    @Log(title = "Get account sub types", businessType = BusinessType.OTHER)
    public Result<List<TextVO>> getAccountSubType() {
        try {

            // 从字典缓存获取指定类型的数据
            List<SysDictData> dictDataList = DictUtils.getDictCache(AccountMainType.NON_STANDARD.name());

            if (dictDataList == null || dictDataList.isEmpty()) {
                return Result.ok(new ArrayList<>());
            }

            // 转换为用户要求的格式：key为英文（dictValue），value为中文（dictLabel）
            List<TextVO> result = dictDataList.stream()
                    .map(dictData -> TextVO.of(dictData.getDictValue(), dictData.getDictLabel()))
                    .collect(Collectors.toList());

            return Result.ok(result);
        } catch (Exception e) {
            log.error("Query account sub types failed for type: {}", AccountMainType.NON_STANDARD.name(), e);
            return Result.fail("获取账户子类型失败");
        }
    }

    /**
     * 获取交付单附件
     *
     */
    @GetMapping("/getDeliverFiles")
    @ApiOperation(value = "获取交付单附件", notes = "根据交付单编号和附件类型，获取附件列表")
    public Result<List<CommonFileVO>> getDeliverFiles(@RequestParam("deliveryOrderNo") String deliveryOrderNo,
                                                      @RequestParam("fileType") @ApiParam("附件类型,1-交付材料，2-人员明细，3-库存，5-标准附件，6-交付附件") Integer fileType) {
        try {

            List<ValueAddedFile> files = valueAddedFileService.getByDeliveryOrderNoAndFileTypes(deliveryOrderNo, fileType);

            return Result.ok(files.stream().map(file -> CommonFileVO.builder().fileName(file.getFileName())
                    .fileUrl(file.getFileUrl()).fileSize(file.getFileSize()).build()).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("get Deliver files failed ,deliverOrderNo: {}", deliveryOrderNo, e);
            return Result.fail("获取交付单附件失败");
        }
    }

    /**
     * 批量导出操作模板
     *
     * @param request 批量导出请求参数
     * @param response HTTP响应
     */
    @PostMapping("/batchExportImportOperationTemplate")
    @ApiOperation(value = "批量导出操作模板", notes = "根据交付单编号列表和操作类型导出对应的Excel模板")
    @Log(title = "Batch export operation template", businessType = BusinessType.EXPORT)
    public void batchExportImportOperationTemplate(@Valid @RequestBody BatchExportTemplateRequest request,
                                                   HttpServletResponse response) {
        try {
            log.info("Batch export operation template request: operation={}, order count={}",
                    request.getOperationDescription(), request.getOrderCount());

            // 调用服务层准备导出数据
            BatchExportTemplateResult result = batchOperationService.batchExportImportOperationTemplate(request);

            // 执行Excel导出
            exportExcelTemplate(result, response);

            log.info("Batch export operation template success: operation={}, exported count={}",
                    request.getOperationDescription(), result.getRecordCount());

        } catch (IllegalArgumentException e) {
            log.warn("Batch export operation template validation failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Batch export operation template failed: operation={}",
                    request != null ? request.getOperationDescription() : "unknown", e);
            throw new RuntimeException("批量导出操作模板失败: " + e.getMessage());
        }
    }

    /**
     * 执行Excel模板导出
     *
     * @param result 导出结果数据
     * @param response HTTP响应
     */
    @SuppressWarnings("unchecked")
    private void exportExcelTemplate(BatchExportTemplateResult result, HttpServletResponse response) {
        try {
            // 使用反射创建ExcelUtil实例
            ExcelUtil<Object> util = new ExcelUtil<>((Class<Object>) result.getDataClass());

            // 执行Excel导出
            util.exportExcel(response, (List<Object>) result.getExportData(),
                           result.getSheetName(), result.getFileName());

            log.info("Excel export completed: fileName={}, recordCount={}",
                    result.getFileName(), result.getRecordCount());

        } catch (Exception e) {
            log.error("Excel export failed: fileName={}", result.getFileName(), e);
            throw new RuntimeException("Excel导出失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入操作（异步执行）
     *
     * @param operation 操作类型
     * @param templateFile 交付单模板Excel文件
     * @param attachmentFile 附件压缩文件（可选）
     * @return 批次号
     */
    @PostMapping("/batchImportOperation")
    @ApiOperation(value = "批量导入操作", notes = "支持交付、补充交付附件、扣款三种操作类型的批量导入，异步执行并返回批次号")
    @Log(title = "Batch import operation", businessType = BusinessType.IMPORT)
    public Result<String> batchImportOperation(
            @RequestParam("operation") @ApiParam(value = "操作类型", required = true, allowableValues = "DELIVERY,SUPPLEMENT_DELIVERY,DEDUCTION") ValueAddedBatchImportOperationType operation,
            @RequestParam("templateFile") @ApiParam(value = "交付单模板Excel文件", required = true) MultipartFile templateFile,
            @RequestParam(value = "attachmentFile", required = false) @ApiParam(value = "附件压缩文件") MultipartFile attachmentFile) {

        try {
            log.info("Starting batch import operation: operation={}", operation.getDescription());
            // 上传模板文件
            R<RemoteAliFileDTO> uploadResult= remoteFileService.uploadFile(templateFile);
            RemoteAliFileDTO templateFileInfo =uploadResult.getData();
            log.info("Template file uploaded: fileName={}, url={}", templateFileInfo.getFileName(), templateFileInfo.getUrl());
            // 上传附件文件（如果存在）
            RemoteAliFileDTO attachmentFileInfo = null;
            if (attachmentFile != null && !attachmentFile.isEmpty()) {
                R<RemoteAliFileDTO> upload= remoteFileService.uploadFile(attachmentFile);
                 attachmentFileInfo =upload.getData();
                log.info("Attachment file uploaded: fileName={}, url={}", attachmentFileInfo.getFileName(), attachmentFileInfo.getUrl());
            }
            // 创建批量导入DTO
            BatchImportOperationDTO importDTO = BatchImportOperationDTO.builder()
                    .operation(operation)
                    .templateFileInfo(templateFileInfo)
                    .attachmentFileInfo(attachmentFileInfo)
                    .build();
            String batchNo = batchOperationService.batchImportOperation(importDTO);
            log.info("Batch import operation started: operation={}, batchNo={}", operation.getDescription(), batchNo);
            return Result.ok(batchNo);
        } catch (IllegalArgumentException e) {
            log.warn("Batch import operation parameter validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch import operation failed: operation={}", operation != null ? operation.getDescription() : "unknown", e);
            return Result.fail("批量导入启动失败: " + e.getMessage());
        }
    }

    /**
     * 获取批量导入进度
     *
     * @param batchNo 批次号
     * @return 进度信息
     */
    @GetMapping("/getBatchImportProgress")
    @ApiOperation(value = "获取批量导入进度", notes = "根据批次号查询批量导入任务的执行进度")
    public Result<BatchImportProgressDTO> getBatchImportProgress(@RequestParam("batchNo") String batchNo) {
        try {
            log.info("Getting batch import progress: batchNo={}", batchNo);
            BatchImportProgressDTO progress = batchOperationService.getBatchImportProgress(batchNo);
            return Result.ok(progress);
        } catch (IllegalArgumentException e) {
            log.warn("Get batch import progress parameter validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Get batch import progress failed: batchNo={}", batchNo, e);
            return Result.fail("获取批量导入进度失败: " + e.getMessage());
        }
    }

    /**
     * 导出批量导入结果
     *
     * @param batchNo 批次号
     * @param response HTTP响应对象
     */
    @GetMapping("/exportBatchImportResult")
    @ApiOperation(value = "导出批量导入结果", notes = "根据批次号导出批量导入的成功和错误记录到Excel文件")
    public void exportBatchImportResult(@RequestParam("batchNo") String batchNo, HttpServletResponse response) {
        try {
            log.info("Exporting batch import result to Excel: batchNo={}", batchNo);
            // 从Service获取数据
            List<BatchImportResultExportDTO> exportData = batchOperationService.getBatchImportResultData(batchNo);
            // 在Controller层处理Excel导出
           ExcelUtil<BatchImportResultExportDTO> util = new ExcelUtil<>(BatchImportResultExportDTO.class);
            util.exportExcel(response, exportData, "批量导入结果_" + batchNo);
            log.info("Batch import result Excel exported successfully: batchNo={}, total records={}",
                    batchNo, exportData.size());

        } catch (Exception e) {
            log.error("Export batch operation errors failed for batchNo: {}", batchNo, e);
            throw new RuntimeException("导出数据失败: " + e.getMessage());
        }
    }

    /**
     * 导出分派异常数据
     *
     * @param batchNo 批次号
     * @param response HTTP响应对象
     */
    @GetMapping("/exportDispatchError")
    @ApiOperation(value = "导出分派异常数据", notes = "根据批次号导出分派操作的异常记录到Excel文件")
    public void exportDispatchError(@RequestParam("batchNo") String batchNo, HttpServletResponse response) {
        try {
            log.info("Exporting dispatch error data to Excel: batchNo={}", batchNo);

            // 从Redis获取异常数据
            List<DispatchErrorDTO> errorList = redisService.getLargeCacheList(
                    CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo,
                    batchOperationConfig.getRedisBatchSize());

            if (errorList == null || errorList.isEmpty()) {
                log.warn("No dispatch error data found for batchNo: {}", batchNo);
                errorList = new ArrayList<>();
            }

            // 导出Excel
            ExcelUtil<DispatchErrorDTO> util = new ExcelUtil<>(DispatchErrorDTO.class);
            util.exportExcel(response, errorList, "分派异常数据_" + batchNo);

        } catch (Exception e) {
            log.error("Failed to export dispatch error data to Excel: batchNo={}, error={}",
                    batchNo, e.getMessage(), e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 保存删除操作的业务日志
     *
     * @param businessId 业务ID
     * @param deliveryOrderNo 交付单编号
     * @param customerName 客户名称
     */
    private void saveDeleteBusinessLog(Long businessId, String deliveryOrderNo, String customerName, Long deptId) {
        try {
            OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
            asyncLogService.saveBusinessLog(new BusinessLogDTO()
                    .setBusinessId(businessId)
                    .setBusinessType(BusinessLogBusinessType.DELIVERY_ORDER.getCode())
                    .setDeptId(operateUserInfo.getDeptId())
                    .setOperType("删除交付单")
                    .setOperName(operateUserInfo.getOperName())
                    .setOperUserId(operateUserInfo.getUserId())
                    .setOperContent(String.format("删除交付单：%s，客户：%s", deliveryOrderNo, customerName)));
        } catch (Exception e) {
            log.error("删除交付单操作日志记录失败: {}", e.getMessage(), e);
        }
    }

}
