package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.ValueAddedAccountPeriodService;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 待提交到已提交状态变更策略
 *
 * 处理从"已保存待提交"状态到"已提交待交付"状态的转换变更
 *
 * 支持的状态转换：
 * SAVED_PENDING_SUBMIT -> SUBMITTED_PENDING_DELIVERY (正常提交)
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class PendingSubmitStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Autowired
    private ValueAddedAccountPeriodService valueAddedAccountPeriodService;

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedSourceStatuses() {
        return Arrays.asList(ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT);
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        // 只处理 SUBMITTED_PENDING_DELIVERY 目标状态
        if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            validateNotEmpty(order.getCustomerName(), "客户企业名称");
            validateCreditCode(order);
            if (order.getValueAddedItemTypeId() == null) {
                throw new IllegalArgumentException("增值事项类型不能为空");
            }

            try {
                valueAddedAccountPeriodService.validateAccountPeriodForSubmit(order);
                valueAddedAccountPeriodService.generateValueAddedPeriodRecords(order);
            } catch (IllegalArgumentException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException("账期处理失败: " + e.getMessage(), e);
            }
        } else {
            throwUnsupportedTransition("待提交", request.getTargetStatus());
        }
    }


}
