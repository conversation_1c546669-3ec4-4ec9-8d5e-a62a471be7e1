package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.DispatchRequestDTO;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderUpsertReq;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderVO;
import com.bxm.customer.domain.vo.valueAdded.SaveStatusReqVO;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.customer.domain.dto.valueAdded.DispatchResultDTO;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 增值交付单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IValueAddedDeliveryOrderService extends IService<ValueAddedDeliveryOrder>
{

    /**
     * 新增或更新增值交付单
     *
     * 支持以下场景：
     * 1. 新增：不提供ID和交付单编号，系统自动生成交付单编号
     * 2. 更新：提供ID或交付单编号，更新现有记录
     * 3. 智能判断：根据客户信息和增值事项判断是否存在重复记录
     *
     * @param orderVO 增值交付单VO对象，包含完整的验证注解和业务字段
     * @return 操作结果，包含交付单编号和操作类型信息
     * @throws IllegalArgumentException 当参数验证失败时抛出（如必填字段为空、格式不正确等）
     * @throws RuntimeException 当业务处理失败时抛出（如数据库操作失败等）
     */
    ValueAddedDeliveryOrder upsert(@Valid @NotNull DeliveryOrderUpsertReq orderVO);

    /**
     * 提交到待交付状态
     *
     * 先调用upsert接口保存数据，然后校验并转换状态为SUBMITTED_PENDING_DELIVERY
     * 复用现有的状态转换策略进行校验，确保状态转换的合法性
     *
     * @param orderVO 增值交付单VO对象，包含完整的验证注解和业务字段
     * @return 操作结果，状态已更新为SUBMITTED_PENDING_DELIVERY的交付单对象
     * @throws IllegalArgumentException 当参数验证失败或状态转换不被允许时抛出
     * @throws RuntimeException 当业务处理失败时抛出（如数据库操作失败等）
     */
    ValueAddedDeliveryOrder submitToPendingDelivery(@Valid @NotNull DeliveryOrderUpsertReq orderVO);

    /**
     * 根据交付单编号查询增值交付单
     *
     * @param deliveryOrderNo 交付单编号
     * @return 增值交付单对象，如果不存在则返回null
     */
    ValueAddedDeliveryOrder getByDeliveryOrderNo(String deliveryOrderNo);

    /**
     * 根据交付单编号列表批量查询增值交付单
     *
     * @param deliveryOrderNos 交付单编号列表
     * @return 增值交付单列表，不存在的交付单编号不会包含在结果中
     */
    List<ValueAddedDeliveryOrder> listByDeliveryOrderNos(List<String> deliveryOrderNos);

    /**
     * 根据交付单编号查询增值交付单详细信息
     *
     * 包含基本信息和关联的扩展数据：
     * - 国税账号对象（bizType=3）
     * - 个税账号对象（bizType=4）
     * - 员工信息列表（根据itemCode动态获取，最多50条）
     * - 交付文件列表（fileType=1）
     *
     * @param deliveryOrderNo 交付单编号
     * @return 增值交付单详细信息VO，如果不存在则返回null
     */
    DeliveryOrderVO getDeliveryOrderVO(String deliveryOrderNo);

    /**
     * 根据客户ID和增值事项查询增值交付单
     *
     * @param customerId 客户ID
     * @param valueAddedItemType 增值事项类型
     * @return 增值交付单对象，如果不存在则返回null
     */
    ValueAddedDeliveryOrder getByCustomerIdAndItemType(Long customerId, Integer valueAddedItemType);



    /**
     * 增值交付单条件分页查询（返回VO对象）
     *
     * 所有条件在 Service 中动态拼接，返回包含itemName映射的VO对象，支持分页
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页VO结果，包含itemName字段映射和分页总数
     */
    IPage<DeliveryOrderVO> queryVOPage(IPage<DeliveryOrderVO> page, DeliveryOrderQuery query);

    /**
     * 修改增值交付单状态
     *
     * 通过状态机管理器执行状态转换，包含完整的业务验证逻辑
     *
     * @param request 状态变更请求，包含交付单编号、目标状态、变更原因等
     * @throws IllegalArgumentException 当参数验证失败或状态转换不被允许时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    void changeStatus(@Valid @NotNull StatusChangeRequestDTO request);

    /**
     * 获取指定交付单的可用状态列表
     *
     * 根据当前状态返回所有可以转换到的目标状态
     *
     * @param deliveryOrderNo 交付单编号
     * @return 可用的状态列表，如果交付单不存在则返回空列表
     */
    List<ValueAddedDeliveryOrderStatus> getAvailableStatuses(String deliveryOrderNo);

    /**
     * 保存状态信息
     *
     * 用于待交付、待扣款状态的保存操作
     * 基于upsert机制实现，先查找记录再进行有针对性的字段更新
     *
     * @param request 保存状态请求，包含交付单编号、目标状态、总扣缴额等信息
     * @throws IllegalArgumentException 当参数验证失败或交付单不存在时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    void saveStatus(@Valid @NotNull SaveStatusReqVO request);

    /**
     * 批量分派增值交付单
     *
     * 将指定的交付单批量分派给指定的会计部门
     * 支持异常收集和缓存，返回详细的分派结果
     *
     * @param deliveryOrders 交付单编号列表
     * @param accountingDeptId 会计部门ID
     * @return 分派结果，包含总数、成功数、失败数和批次号
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    DispatchResultDTO batchDispatch(@NotNull List<String> deliveryOrders, @NotNull Long accountingDeptId, DispatchRequestDTO request);

}
