package com.bxm.customer.service;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.StatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 状态机管理器
 *
 * 统一管理增值交付单的状态转换逻辑
 * 负责协调各个状态变更策略，执行状态转换操作
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Service
public class ValueAddedDeliveryOrderStateMachineManager {

    @Autowired
    private List<StatusChangeStrategy> changeStrategies;

    /**
     * 初始化后变更策略注入情况
     */
    @PostConstruct
    public void init() {
        log.info("StateMachineManager initialized with {} change strategies:", changeStrategies.size());
        for (StatusChangeStrategy strategy : changeStrategies) {
            log.info("- {}: supports current status {}",strategy.getStrategyName(), strategy.getSupport().getDescription());
        }
    }

    /**
     * 验证并执行状态变更（不包含数据库操作）
     *
     * @param order 交付单实体
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @param request 状态变更请求
     * @throws IllegalArgumentException 当验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    public void validateAndChangeStatus(ValueAddedDeliveryOrder order,
                                      ValueAddedDeliveryOrderStatus currentStatus,
                                      ValueAddedDeliveryOrderStatus targetStatus,
                                      StatusChangeRequestDTO request) {

        // 1. 根据当前状态查找策略
        StatusChangeStrategy strategy = findChangeStrategy(currentStatus);
        if (strategy == null) {
            throw new IllegalArgumentException(
                String.format("未找到支持当前状态 %s 的变更策略", currentStatus.getDescription()));
        }

        // 2. 执行验证（包含状态转换合法性验证）
        try {
            //strategy.validate(order, request);
            log.info("Status change validation passed for order: {} using strategy: {}", order.getDeliveryOrderNo(), strategy.getStrategyName());
        } catch (Exception e) {
            log.error("Status change validation failed for order: {} using strategy: {}, error: {}",order.getDeliveryOrderNo(), strategy.getStrategyName(), e.getMessage());
            throw e;
        }

        // 3. 更新状态和备注字段（在调用方的Service中会保存到数据库）
        order.setStatus(targetStatus.getCode());

        // 更新操作备注和交付备注
        if (request.getRemark() != null) {
            order.setRemark(request.getRemark());
        }
        if (request.getDeliveryRemark() != null) {
            order.setDeliveryRemark(request.getDeliveryRemark());
        }
        if (request.getTotalWithholdingAmount()!=null){
            order.setTotalWithholdingAmount(request.getTotalWithholdingAmount());
        }

    }

    /**
     * 获取指定状态的可用下一状态列表
     *
     * @param currentStatus 当前状态
     * @return 可用的下一状态列表
     */
    public List<ValueAddedDeliveryOrderStatus> getAvailableNextStatuses(ValueAddedDeliveryOrderStatus currentStatus) {
        if (currentStatus == null) {
            return new ArrayList<>();
        }

        List<ValueAddedDeliveryOrderStatus> availableStatuses = new ArrayList<>();

        // 遍历所有可能的目标状态
        for (ValueAddedDeliveryOrderStatus targetStatus : ValueAddedDeliveryOrderStatus.values()) {
            if (targetStatus == currentStatus) {
                continue; // 跳过相同状态
            }

            // 检查是否有策略支持此转换
            StatusChangeStrategy strategy = findChangeStrategy(currentStatus);
            if (strategy != null && strategy.isValidTransition(currentStatus, targetStatus)) {
                availableStatuses.add(targetStatus);
            }
        }

        return availableStatuses;
    }

    /**
     * 根据当前状态查找对应的变更策略
     *
     * @param currentStatus 当前状态
     * @return 支持的变更策略，如果没有则返回null
     */
    private StatusChangeStrategy findChangeStrategy(ValueAddedDeliveryOrderStatus currentStatus) {
        return changeStrategies.stream()
                .filter(strategy -> strategy.supports(currentStatus))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有状态的转换规则映射
     *
     * @return 状态转换规则映射 (当前状态 -> 可转换状态列表)
     */
    public Map<ValueAddedDeliveryOrderStatus, List<ValueAddedDeliveryOrderStatus>> getAllTransitionRules() {
        return Arrays.stream(ValueAddedDeliveryOrderStatus.values())
                .collect(Collectors.toMap(
                    status -> status,
                    this::getAvailableNextStatuses
                ));
    }


}
