package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 已扣款状态变更策略
 *
 * 处理从"已扣款"状态到其他状态的转换变更
 *
 * 注意：已扣款是终态状态，一般情况下不允许转换到其他状态
 * 仅在特殊情况下允许转换：
 * 1. DEDUCTION_COMPLETED -> DEDUCTION_EXCEPTION (发现扣款异常，需要处理)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class DeductionCompletedStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedSourceStatuses() {
        return Arrays.asList(ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED);
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            validateRemark(request.getRemark(), "从终态转换");
        } else {
            throwUnsupportedTransition("已扣款", request.getTargetStatus());
        }
    }


}
